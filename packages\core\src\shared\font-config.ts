/**
 * Copyright 2023-present DreamNum Co., Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * 统一的字体配置文件
 * 所有字体相关的常量都应该在这里定义，确保整个应用使用一致的字体设置
 */

// 默认主字体
export const DEFAULT_FONT_FAMILY = 'Arial';

// 字体回退栈，包含多语言支持
export const DEFAULT_FONTFACE_PLANE =
    '"Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif';

// 完整的字体栈，包含主字体和回退字体
export const FULL_FONT_STACK = `${DEFAULT_FONT_FAMILY}, ${DEFAULT_FONTFACE_PLANE}`;

// 默认字体大小
export const DEFAULT_FONT_SIZE = 11;

/**
 * 获取标准化的字体字符串，用于Canvas等需要完整字体定义的场景
 * @param fontSize 字体大小（像素）
 * @param fontFamily 可选的字体族，默认使用DEFAULT_FONT_FAMILY
 * @param bold 是否加粗
 * @param italic 是否斜体
 * @returns 标准化的字体字符串
 */
export function getStandardFontString(
    fontSize: number,
    fontFamily: string = DEFAULT_FONT_FAMILY,
    bold: boolean = false,
    italic: boolean = false
): string {
    let fontStyle = '';
    if (italic) fontStyle += 'italic ';
    if (bold) fontStyle += 'bold ';
    fontStyle += `${fontSize}px ${fontFamily}`;
    return fontStyle;
}

/**
 * 获取带回退的完整字体字符串
 * @param fontSize 字体大小（像素）
 * @param fontFamily 主字体族
 * @param bold 是否加粗
 * @param italic 是否斜体
 * @returns 包含回退字体的完整字体字符串
 */
export function getFullFontString(
    fontSize: number,
    fontFamily: string = DEFAULT_FONT_FAMILY,
    bold: boolean = false,
    italic: boolean = false
): string {
    let fontStyle = '';
    if (italic) fontStyle += 'italic ';
    if (bold) fontStyle += 'bold ';
    fontStyle += `${fontSize}px ${fontFamily}, ${DEFAULT_FONTFACE_PLANE}`;
    return fontStyle;
}
