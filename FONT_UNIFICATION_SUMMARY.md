# 字体统一配置总结

## 概述

本次修改统一了整个Univer代码库中的字体设置，确保所有地方都使用一致的字体族配置。

## 主要问题

在修改前，代码库中存在以下字体不一致的问题：

1. **水印功能硬编码字体**：在 `packages/engine-render/src/components/sheets/watermark/util.ts` 中直接硬编码使用 `Arial` 字体
2. **重复的字体配置定义**：在多个文件中重复定义了相同的字体回退栈
3. **缺乏统一的字体管理**：没有中央化的字体配置管理

## 解决方案

### 1. 创建统一的字体配置文件

创建了 `packages/core/src/shared/font-config.ts` 文件，包含：

- `DEFAULT_FONT_FAMILY`: 默认主字体 ('Arial')
- `DEFAULT_FONTFACE_PLANE`: 字体回退栈
- `FULL_FONT_STACK`: 完整的字体栈
- `getStandardFontString()`: 获取标准化字体字符串的工具函数
- `getFullFontString()`: 获取带回退的完整字体字符串的工具函数

### 2. 修改的文件

#### 核心配置文件
- `packages/core/src/shared/font-config.ts` - 新建统一字体配置文件
- `packages/core/src/index.ts` - 导出字体配置常量和函数

#### 水印功能
- `packages/engine-render/src/components/sheets/watermark/util.ts` - 使用统一的字体配置函数

#### 字体库
- `packages/engine-render/src/components/docs/layout/shaping-engine/font-library.ts` - 使用统一的默认字体

#### 常量文件
- `packages/engine-render/src/basics/const.ts` - 重新导出统一的字体配置

## 当前字体配置

### 默认字体族
- **主字体**: Arial
- **回退字体栈**: "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif

### 字体列表（UI组件）
在 `packages/ui/src/components/font-family/interface.ts` 中定义的可选字体：
- Arial
- Times New Roman
- Tahoma
- Verdana
- Microsoft YaHei
- SimSun
- SimHei
- Kaiti
- FangSong
- NSimSun
- STXinwei
- STXingkai
- STLiti

## 使用方法

### 在Canvas绘制中使用统一字体

```typescript
import { getStandardFontString } from '@univerjs/core';

// 设置Canvas字体
ctx.font = getStandardFontString(fontSize, fontFamily, bold, italic);
```

### 获取完整字体栈

```typescript
import { getFullFontString } from '@univerjs/core';

// 获取带回退的完整字体字符串
const fontString = getFullFontString(fontSize, fontFamily, bold, italic);
```

### 使用默认字体常量

```typescript
import { DEFAULT_FONT_FAMILY, DEFAULT_FONTFACE_PLANE } from '@univerjs/core';

// 使用默认字体
const defaultFont = DEFAULT_FONT_FAMILY; // 'Arial'

// 使用字体回退栈
const fallbackFonts = DEFAULT_FONTFACE_PLANE;
```

## 优势

1. **一致性**: 所有组件使用相同的字体配置
2. **可维护性**: 字体配置集中管理，易于修改
3. **可扩展性**: 提供工具函数，便于添加新的字体配置需求
4. **多语言支持**: 字体回退栈包含中文、日文、韩文等多语言字体

## 注意事项

1. 所有新的字体相关代码都应该使用 `packages/core/src/shared/font-config.ts` 中的配置
2. 避免在代码中硬编码字体名称
3. 如需修改默认字体，只需在 `font-config.ts` 中修改即可
4. 水印功能现在使用统一的字体配置，确保与其他组件的一致性

## 验证

修改完成后，所有字体相关的功能都应该：
1. 使用统一的默认字体 (Arial)
2. 在字体不可用时正确回退到备选字体
3. 保持视觉一致性

建议运行相关测试确保字体渲染功能正常工作。
